# 警報數量顯示修正 - 2025/08/14

## 問題描述
系統中有一個功能，在PHP頁面有一個是否觸發警報的選項，如果勾起來的話，觸發時，左側選單會顯示一個數字代表樓層裡觸發警報的數量，但是有時候有勾也有觸發，數量卻沒有顯示出來。

## 問題分析
經過深入分析程式碼，「有時不顯示」的問題原因尚未完全確定，但可能的原因包括：

### 1. 間歇性資料問題
可能的問題來源：

- **資料庫查詢間歇性返回空結果**：某些情況下查詢可能沒有匹配到資料
- **網路請求間歇性失敗**：AJAX請求可能偶爾超時或失敗
- **前端DOM操作問題**：JavaScript執行時DOM元素可能尚未準備好

### 2. 前端JavaScript清除邏輯問題
在 `web/com_floor-1.0.0/site/views/sroots/tmpl/default.php` 中：

- 當警報數量更新時，舊的建築物警報數量沒有被清除
- 當 `allBuildingAlarmCount` 為空陣列時，`forEach` 不會執行，舊數字不會被清除
- 導致警報解除後，舊的數字仍然顯示

### 3. 警報抑制機制（已確認不是問題）
用戶已確認 `#__timing_set` 表中沒有任何警報抑制設定，所以這不是問題原因。

## 修正內容

### 1. 添加調試功能
**檔案**: `web/com_floor-1.0.0/site/views/sroots/tmpl/default.php` (第1831-1845行)

**修正**: 添加前端調試日誌
```javascript
// 添加調試日誌
console.log("allBuildingAlarmCount:", response.allBuildingAlarmCount);
response.allBuildingAlarmCount.forEach(buildingAlarmCount => {
    var element = jQuery('#building_alarm_count_' + buildingAlarmCount.building);
    console.log("Processing building:", buildingAlarmCount.building, "count:", buildingAlarmCount.alarm_count, "element found:", element.length > 0);
    if (element.length > 0) {
        element.text("(" + buildingAlarmCount.alarm_count +")");
        element.addClass('alarm_node');
        console.log("Updated building alarm count for building:", buildingAlarmCount.building);
    } else {
        console.log("Element not found for building:", buildingAlarmCount.building);
    }
});
```

**檔案**: `web/com_floor-1.0.0/site/controllers/sroots.php` (第2544-2547行)

**修正**: 添加後端調試日誌
```php
// 添加調試日誌
error_log("Building alarm counts: " . json_encode($build_alarm_counts));
error_log("Mute nodes count: " . count($mute_nodes));
```

### 2. 修正前端清除邏輯
**檔案**: `web/com_floor-1.0.0/site/views/sroots/tmpl/default.php` (第1789-1790行)

**新增**:
```javascript
// 清除所有建築物警報數量顯示
jQuery('.building_alarm_count').text('');
jQuery('.building_alarm_count').removeClass('alarm_node');
```

### 3. 添加錯誤處理
**檔案**: `web/com_floor-1.0.0/site/views/sroots/tmpl/default.php`

**修正**: 添加timeout設定和JSON解析錯誤處理
```javascript
timeout: 5000, // 設定5秒timeout
// 添加try-catch處理JSON解析錯誤
```

## 修正說明

### 問題根本原因
「有時不顯示」的問題根本原因尚未完全確定，可能的原因：

1. **間歇性資料問題**:
   - 資料庫查詢偶爾返回空結果
   - 網路請求間歇性失敗
   - 前端JavaScript執行時序問題

2. **前端清除邏輯缺失**: 當警報數量變為0時，前端沒有清除舊的顯示數字

### 診斷方法
1. **添加調試日誌**: 在前端和後端都添加詳細的日誌記錄
2. **監控請求**: 檢查AJAX請求是否有失敗的情況
3. **檢查資料**: 確認資料庫查詢結果是否穩定
4. **錯誤處理**: 添加timeout和JSON解析錯誤處理

### JavaScript清除邏輯修正
**解決方案**: 在每次更新警報數量前，先清除所有建築物警報數量的顯示
- **清除文字**: 使用 `jQuery('.building_alarm_count').text('')` 清除所有數字
- **移除樣式**: 使用 `removeClass('alarm_node')` 移除警報樣式
- **重新顯示**: 如果有警報，`forEach` 迴圈會重新設定正確的數量和樣式

## 預期效果
修正後，警報數量顯示功能應該能夠：
1. **提供診斷資訊**: 通過調試日誌了解問題發生的具體情況
2. **正確清除過期顯示**: 當警報數量變為0時，正確清除舊的數字
3. **更好的錯誤處理**: 當請求失敗時，提供詳細的錯誤資訊
4. **問題追蹤**: 能夠追蹤到問題發生的具體原因

## 測試建議
1. **監控調試日誌**:
   - 檢查瀏覽器控制台的日誌輸出
   - 檢查伺服器錯誤日誌（通常在 `/var/log/apache2/error.log` 或類似位置）
   - 觀察「有時不顯示」發生時的日誌內容
2. **測試清除功能**:
   - 當所有警報解除時，檢查數字是否完全清除
   - 當部分警報解除時，檢查數字是否正確更新
3. **長期監控**: 持續觀察系統運行，記錄問題發生的模式

## 下一步診斷
根據調試日誌的結果，可能需要進一步檢查：
1. **如果後端日誌顯示查詢結果為空**: 檢查資料庫資料和查詢條件
2. **如果前端日誌顯示元素找不到**: 檢查DOM結構和元素ID
3. **如果請求失敗**: 檢查網路連接和伺服器狀態
4. **如果資料正常但顯示異常**: 檢查CSS樣式和JavaScript執行順序
