# 警報音效檔案變更處理修正記錄 - 1.0.67分支

**修改時間：** 2025年1月13日  
**分支：** 1.0.67  
**問題描述：** 警報狀態沒變但音效檔案變更時，持續播放檢查無法載入新的音效檔案  
**修改人員：** Augment Agent  

## 問題分析

### 問題場景
```
警報狀態：is_alarm = 1, obj.is_need_alarm = 1 (狀態沒變)
音效檔案：從 'alarm1.mp3' 變成 'alarm2.mp3'

之前的邏輯：
1. if(is_alarm != obj.is_need_alarm) → 1 != 1 = false，不執行狀態變化處理
2. 持續播放檢查：if(obj.is_need_alarm == 1) → 會執行
3. 但持續播放檢查只會播放舊的音效檔案，不會載入新的音效檔案
```

**結果：** 音效檔案變了，但還是播放舊的音效！

### 根本原因
持續播放檢查邏輯過於簡單，沒有考慮音效檔案路徑的變化：

```javascript
// 之前的邏輯 - 有問題
if(obj.is_need_alarm == 1) {
    if(myalarm[0].paused || myalarm[0].ended) {
        myalarm[0].play();  // 只是重新播放，沒檢查檔案是否變更
    }
}
```

## 修正內容

### 修正邏輯：加入音效檔案變更檢查

在持續播放檢查中加入音效檔案路徑比較：

**檔案：** `web/com_whome-1.0.0/site/views/roots/tmpl/default.php`
**位置：** 第925-950行

```javascript
// 修正：警報持續期間確保音效正常播放
if(obj.is_need_alarm == 1) {
    var myalarm = jQuery("#myalarm");
    var myalarm_source = myalarm.children('source').eq(0);
    var path = '/images/alarm_audios/' + obj.alarm_sound_file;
    
    // 檢查音效檔案是否有變化
    if (myalarm_source.attr('src') != path) {
        // 音效檔案變了，載入新音效
        myalarm_source.attr('src', path);
        myalarm[0].pause();
        myalarm[0].currentTime = 0;
        
        myalarm[0].addEventListener('canplaythrough', function() {
            myalarm[0].play();
        }, { once: true });
        
        myalarm[0].load();
    } else {
        // 音效檔案沒變，如果音效已停止或播放完畢，重新播放
        if(myalarm[0].paused || myalarm[0].ended) {
            myalarm[0].currentTime = 0;
            myalarm[0].play();
        }
    }
}
```

**檔案：** `web/com_floor-1.0.0/site/views/sroots/tmpl/default.php`
**位置：** 第2500-2525行

```javascript
// 修正：警報持續期間確保音效正常播放
if(obj.is_need_alarm == 1) {
    var myalarm = jQuery("#myalarm");
    var myalarm_source = myalarm.children('source').eq(0);
    var path = '/images/alarm_audios/' + obj.alarm_sound_file;
    
    // 檢查音效檔案是否有變化
    if (myalarm_source.attr('src') != path) {
        // 音效檔案變了，載入新音效
        myalarm_source.attr('src', path);
        myalarm[0].pause();
        myalarm[0].currentTime = 0;
        
        myalarm[0].addEventListener('canplaythrough', function() {
            myalarm[0].play();
        }, { once: true });
        
        myalarm[0].load();
    } else {
        // 音效檔案沒變，如果音效已停止或播放完畢，重新播放
        if(myalarm[0].paused || myalarm[0].ended) {
            myalarm[0].currentTime = 0;
            myalarm[0].play();
        }
    }
}
```

## 修正邏輯說明

### 雙重檢查機制
1. **檢查音效檔案路徑**：`myalarm_source.attr('src') != path`
2. **檢查音效播放狀態**：`myalarm[0].paused || myalarm[0].ended`

### 處理分支
1. **音效檔案變更**：
   - 更新音效檔案路徑
   - 停止當前播放
   - 重置播放位置
   - 載入新音效並播放

2. **音效檔案未變更**：
   - 檢查播放狀態
   - 如果已停止或播放完畢，重新播放

## 修正效果

### 修正前的問題
- ❌ 警報狀態沒變但音效檔案變更時，無法載入新音效
- ❌ 持續播放舊的音效檔案
- ❌ 用戶聽不到正確的警報音效

### 修正後的改善
- ✅ **正確處理音效檔案變更**：即使警報狀態沒變，音效檔案變更也會被檢測到
- ✅ **自動載入新音效**：檢測到檔案變更時會自動載入並播放新音效
- ✅ **保持持續播放**：音效檔案沒變時仍會確保持續播放
- ✅ **避免重複載入**：只有在檔案真正變更時才重新載入

## 完整的處理邏輯

### 狀態變化處理（原有邏輯）
```javascript
if(is_alarm != obj.is_need_alarm) {
    // 處理警報狀態變化
    // 包含音效檔案載入和播放
}
```

### 持續播放檢查（修正後邏輯）
```javascript
if(obj.is_need_alarm == 1) {
    if (音效檔案變更) {
        // 載入新音效並播放
    } else if (音效已停止) {
        // 重新播放現有音效
    }
}
```

## 涵蓋的所有情況

1. **新警報產生** (0→1) ✅ 狀態變化處理 + 持續播放檢查
2. **警報解除** (1→0) ✅ 狀態變化處理（停止音效）
3. **沒有警報** (0→0) ✅ 不執行任何處理
4. **警報持續，音效檔案不變** (1→1, 同檔案) ✅ 持續播放檢查（重新播放）
5. **警報持續，音效檔案變更** (1→1, 不同檔案) ✅ 持續播放檢查（載入新音效）

## 測試建議

1. **測試音效檔案變更**：在警報持續期間變更音效檔案，確認會播放新音效
2. **測試持續播放**：確認音效播放完畢後會自動重新播放
3. **測試狀態變化**：確認警報狀態變化時音效處理正常
4. **測試檔案不存在**：確認音效檔案不存在時的錯誤處理

## 注意事項

此修正確保了音效檔案變更的正確處理，但需要注意：
1. 音效檔案路徑必須正確
2. 新的音效檔案必須存在且可播放
3. 瀏覽器必須支援新音效檔案的格式
